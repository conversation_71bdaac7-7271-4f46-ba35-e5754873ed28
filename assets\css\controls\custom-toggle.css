.customize-control-toggle .toggle--wrapper {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  flex-direction: row;
  -webkit-box-direction: normal;
  -webkit-box-orient: horizontal;
  -webkit-box-pack: start;
  -webkit-flex-direction: row;
  -ms-flex-direction: row;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  margin: 8px 0;
}
.customize-control-toggle .toggle--wrapper .customize-control-title {
  -webkit-box-flex: 2;
  -webkit-flex: 2 0 0;
  -ms-flex: 2 0 0;
  flex: 2 0 0;
}
.customize-control-toggle .toggle--wrapper input[type=checkbox] {
  display: none;
}
.customize-control-toggle {
  margin-bottom: 0
}
.customize-control-toggle .toggle--wrapper label {
  border-radius: 14px;
  cursor: pointer;
  display: inline-block;
  height: 20px;
  outline: none;
  position: relative;
  right: 0px;
  top: 2px;
  -webkit-transition: background 0.2s ease;
  transition: background 0.2s ease;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 34px;
}
.customize-control-toggle .toggle--wrapper label::after,
.customize-control-toggle .toggle--wrapper label::before {
  content: "";
  display: block;
  position: absolute;
}
.customize-control-toggle .toggle--wrapper label::after {
  background: #fff;
  border-radius: 50%;
  box-sizing: border-box;
  height: 12px;
  left: 4px;
  top: 4px;
  -webkit-transition: background 0.2s ease, -webkit-transform 0.2s ease;
  transition: background 0.2s ease, -webkit-transform 0.2s ease;
  transition: transform 0.2s ease, background 0.2s ease;
  transition: transform 0.2s ease, background 0.2s ease, -webkit-transform 0.2s ease;
  width: 12px;
}
.customize-control-toggle .toggle--wrapper label::before {
  background-color: #ccc;
  border-radius: 60px;
  bottom: 2px;
  left: 2px;
  right: 2px;
  top: 2px;
  -webkit-transition: background 0.2s ease;
  transition: background 0.2s ease;
}
.customize-control-toggle .toggle--wrapper input[type=checkbox]:checked + label,.customize-control-toggle .toggle--wrapper input[type=checkbox]:checked + label::before  {
  background-color: #00aadc;
}
.customize-control-toggle .toggle--wrapper input[type=checkbox]:checked + label::after {
  background-color: #fff;
  border: 2px solid #fff;
  -webkit-transform: translateX(12px);
  -ms-transform: translateX(12px);
  transform: translateX(12px);
}
.customize-control-toggle:hover .toggle--wrapper label::before {
  background-color: #d9dcdf;
}
