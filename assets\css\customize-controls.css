/* ---------Customizer--------- */
.control-section-restaurant-fast-food h3.accordion-section-title {
	color: #F49B3F !important;
	padding: 15px;
	border: 2px solid #F49B3F !important;
	border-left: none !important;
	border-right: none !important;
	line-height: 2;
}
a.button.button-secondary.alignright {
    color: #fff;
    box-shadow: none;
    font-weight: bold;
    padding: 0 10px;
    background: transparent;
    height: 31px;
    background: #333333;
    border: none;
}
a.button.button-secondary.alignright {
    color: #fff;
    box-shadow: none;
    font-weight: bold;
    padding: 0 10px;
    background: transparent;
    height: 31px;
    background: #5EAE53;
    border: none;
}

span.customize-control-title, .customize-control-title {
    background: #bbb;
	letter-spacing: 2px;
    font-weight: 500;
    font-size: 12px;
    padding: 5px;
	color: #fff;
	margin-bottom: 12px;
	text-transform: uppercase;
}

/* span.customize-control-title */
.customize-control-toggle .toggle--wrapper span.customize-control-title{
	background: none;
	color: #50575e;
	padding: 0px;
	text-transform: none;
}
