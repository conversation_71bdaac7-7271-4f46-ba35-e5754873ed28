<?php

/**
 * Template Name: Front Page
 *
 * @package Restaurant Fast Food
 * @subpackage restaurant_fast_food
 */

get_header(); ?>

<!-- Slide Hero Section -->
<div class="slide-hero">
    <!-- Carousel -->
    <div id="heroCarousel" class="carousel slide" data-ride="carousel">
        <div class="carousel-inner">
            <!-- Add your carousel images here -->
            <div class="carousel-item active">
                <a><img src="<?php echo get_template_directory_uri(); ?>/assets/images/img1.jpg" class="d-block w-100" alt="Slide 1" data-image="img1"></a>
            </div>
            <div class="carousel-item">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/img2.jpg" class="d-block w-100" alt="Slide 2">
            </div>
            <div class="carousel-item">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/img3.jpg" class="d-block w-100" alt="Slide 3">
            </div>
            <div class="carousel-item">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/img4.jpg" class="d-block w-100" alt="Slide 4" data-image="img4">
            </div>
            <div class="carousel-item">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/img5.jpg" class="d-block w-100" alt="Slide 5">
            </div>
            <div class="carousel-item">
                <img src="<?php echo get_template_directory_uri(); ?>/assets/images/img6.jpg" class="d-block w-100" alt="Slide 6">
            </div>
        </div>
        <a class="carousel-control-prev" href="#heroCarousel" role="button" data-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="sr-only">Previous</span>
        </a>
        <a class="carousel-control-next" href="#heroCarousel" role="button" data-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="sr-only">Next</span>
        </a>
    </div>
    <!-- Floating Search Widget -->
    <div class="search-widget">
        <?php get_search_form(); ?>
    </div>

</div>

<!-- Main Content Section -->
<div class="container mt-5">
    <!-- Relevant News Section -->
    <div id="relevant-news">
        <h2>Relevant News</h2>
        <!-- Content to be filled in the future -->
        <!-- Insertar video subido -->
        <div class="video-container">
            <video width="560" height="415" controls>
                <source src="https://www.servicesandproducts.ca/images/SmallVersion.mp4" type="video/mp4">
                Tu navegador no soporta el elemento de video.
            </video>
        </div>
    </div>

    <div class="row mt-5">
        <!-- Latest Business Posts -->
        <div class="col-lg-6 page-latest">
            <h3><a href="<?php echo get_post_type_archive_link('business'); ?>">Latest Business</a></h3>
            <?php
            $business_args = array(
                'post_type'      => 'business',
                'posts_per_page' => 2,
                'orderby'        => 'date',
                'order'          => 'DESC',
            );
            $business_posts = new WP_Query($business_args);
            if ($business_posts->have_posts()) :
                while ($business_posts->have_posts()) : $business_posts->the_post(); ?>
                    <div class="business-post">
                        <h4><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                        <p><?php echo wp_trim_words(get_the_content(), 20); ?></p>
                    </div>
                <?php endwhile;
                wp_reset_postdata();
            else : ?>
                <p>No business posts found.</p>
            <?php endif; ?>
        </div>

        <!-- Latest Professional Posts -->
        <div class="col-lg-6 page-latest">
            <h3><a href="<?php echo get_post_type_archive_link('professional'); ?>">Latest Professional</a></h3>
            <?php
            $professional_args = array(
                'post_type'      => 'professional',
                'posts_per_page' => 2,
                'orderby'        => 'date',
                'order'          => 'DESC',
            );
            $professional_posts = new WP_Query($professional_args);
            if ($professional_posts->have_posts()) :
                while ($professional_posts->have_posts()) : $professional_posts->the_post(); ?>
                    <div class="professional-post">
                        <h4><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                        <p><?php echo wp_trim_words(get_the_content(), 20); ?></p>
                    </div>
                <?php endwhile;
                wp_reset_postdata();
            else : ?>
                <p>No professional posts found.</p>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Contador de visitas -->
<div class="visit-counter">
    <p><?php echo get_post_views(get_the_ID()); ?></p>
</div>


<!-- Seccion de JavaScripts -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.search-form');
    const searchField = form.querySelector('.search-field');
    
    form.addEventListener('submit', function(event) {
        if (searchField.value.trim() === '') {
            event.preventDefault(); // Previene el envío del formulario
            alert('Por favor, ingresa un término de búsqueda.'); // Mensaje opcional
        }
    });
});

document.addEventListener('DOMContentLoaded', function () {
    // Initialize Bootstrap Carousel
    const heroCarousel = document.getElementById('heroCarousel');
    if (heroCarousel && typeof jQuery !== 'undefined' && jQuery.fn.carousel) {
        jQuery('#heroCarousel').carousel({
            interval: 5000,  // Auto-slide every 5 seconds
            pause: 'hover',  // Pause on hover
            wrap: true,      // Loop continuously
            keyboard: true   // Enable keyboard navigation
        });
    }

    // Handle image clicks
    const carouselImages = document.querySelectorAll('.carousel-item img');

    carouselImages.forEach(img => {
        img.addEventListener('click', function () {
            const imageId = this.getAttribute('data-image');

            // Verifica si el atributo data-image no es null ni vacío
            if (imageId !== null && imageId !== '') {
                // Redirige a la página coupons con el ID de la imagen como parámetro
                window.location.href = `/coupons?image_id=${imageId}`;
            } else {
                // No hace nada si data-image es null o vacío
                console.log('Esta imagen no tiene un ID válido.');
            }
        });
    });
});
</script>



<?php get_footer(); ?>