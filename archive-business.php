<?php
/**
 * The template for displaying Business archive pages
 *
 * @package Restaurant Fast Food
 * @subpackage restaurant_fast_food
 */

get_header(); ?>

<div class="container">
  <main id="tp_content" role="main">
    <div id="primary" class="content-area">
      <?php if (have_posts()) : ?>
        <div class="page-header">
          <?php
          the_archive_title('<h1 class="page-title">', '</h1>');
          the_archive_description('<div class="taxonomy-description">', '</div>');
          ?>
        </div>
      <?php endif; ?>

      <?php
      $restaurant_fast_food_sidebar_layout = get_theme_mod('restaurant_fast_food_sidebar_post_layout', 'right');
      
      // Configuración del loop principal
      $args = array(
        'post_type' => 'business',
        'posts_per_page' => -1,
        'orderby' => 'title',
        'order' => 'ASC'
      );
      $query = new WP_Query($args);
      if ($restaurant_fast_food_sidebar_layout !== 'full') {
        echo '<div class="row m-0">';
        if ($restaurant_fast_food_sidebar_layout == 'left' || $restaurant_fast_food_sidebar_layout == 'right') {
          echo '<div class="col-lg-8 col-md-8">';
        } elseif ($restaurant_fast_food_sidebar_layout == 'three-column' || $restaurant_fast_food_sidebar_layout == 'four-column') {
          echo '<div class="col-lg-6 col-md-6">';
        } elseif ($restaurant_fast_food_sidebar_layout == 'grid') {
          echo '<div class="col-lg-9 col-md-9">';
        } else {
          echo '<div class="col-lg-8 col-md-8">';
        }

        // Segundo loop para la segunda zona de visualización si es necesario
        if ($query->have_posts()) :
          $current_letter = '';
          while ($query->have_posts()) : $query->the_post();
            $first_letter = strtoupper(substr(get_the_title(), 0, 1));
            if ($first_letter !== $current_letter) {
              if ($current_letter !== '') {
                echo '</ul>';
              }
              $current_letter = $first_letter;
              echo '<h2 class="group-letter">' . $current_letter . '</h2>';
              echo '<ul>';
            }
            echo '<li>';
            get_template_part('template-parts/post/content-business', get_post_format());
            echo '</li>';
          endwhile;
          echo '</ul>';
          the_posts_navigation();
        else :
          get_template_part('template-parts/post/content', 'none');
        endif;

        wp_reset_postdata();
        echo '</div>'; // Cerrar la columna principal

        // Sidebar según el layout seleccionado
        echo '<div class="col-lg-4 col-md-4" id="theme-sidebar">';
        if ($restaurant_fast_food_sidebar_layout == 'left' || $restaurant_fast_food_sidebar_layout == 'right') {
          get_sidebar();
        } elseif ($restaurant_fast_food_sidebar_layout == 'three-column' || $restaurant_fast_food_sidebar_layout == 'four-column') {
          dynamic_sidebar('sidebar-2');
        } elseif ($restaurant_fast_food_sidebar_layout == 'grid') {
          get_sidebar();
        } else {
          get_sidebar();
        }
        echo '</div>'; // Cerrar la columna de la barra lateral
        echo '</div>'; // Cerrar el contenedor de fila
      }
      ?>
    </div>
  </main>
</div>

<?php get_footer(); ?>
