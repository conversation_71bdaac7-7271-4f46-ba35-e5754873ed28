<?php
/**
 * Template part for displaying posts
 *
 * @package Restaurant Fast Food
 * @subpackage restaurant_fast_food
 */

?>
<article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
    <h2><?php the_title(); ?></h2>
    <div class="box-info">
        <?php $blog_archive_ordering = get_theme_mod('blog_meta_order', array('date', 'author', 'comment', 'category'));
        foreach ($blog_archive_ordering as $blog_data_order) : 
            if ('date' === $blog_data_order) : ?>
                <i class="far fa-calendar-alt mb-1"></i><span class="entry-date"><?php echo get_the_date('j F, Y'); ?></span>
            <?php elseif ('author' === $blog_data_order) : ?>
                <i class="fas fa-user mb-1"></i><span class="entry-author"><?php the_author(); ?></span>
            <?php elseif ('comment' === $blog_data_order) : ?>
                <i class="fas fa-comments mb-1"></i><span class="entry-comments"><?php comments_number(__('0 Comments', 'restaurant-fast-food'), __('0 Comments', 'restaurant-fast-food'), __('% Comments', 'restaurant-fast-food')); ?></span>
            <?php elseif ('category' === $blog_data_order) :?>
                <i class="fas fa-list mb-1"></i><span class="entry-category"><?php restaurant_fast_food_display_post_category_count(); ?></span>
            <?php endif;
        endforeach; ?>
    </div>
    <hr>
    <div class="box-image mb-2">
        <?php
        $thumbnailPrinted = false; // Variable para rastrear si ya se imprimió el thumbnail

        $relationProfessional = get_field('relation');
        if (!$relationProfessional && !$thumbnailPrinted) {
            the_post_thumbnail();
            $thumbnailPrinted = true; // Marcar que ya se imprimió el thumbnail
        }elseif($relationProfessional && !$thumbnailPrinted) {
            the_post_thumbnail();
            $thumbnailPrinted = true; // Marcar que ya se imprimió el thumbnail
        }

        $relationBusiness = get_field('relationbusiness');
        if (!$relationBusiness && !$thumbnailPrinted) {
            the_post_thumbnail();
            $thumbnailPrinted = true; // Marcar que ya se imprimió el thumbnail
        }elseif($relationBusiness && !$thumbnailPrinted) {
            the_post_thumbnail();
            $thumbnailPrinted = true; // Marcar que ya se imprimió el thumbnail
        }
        ?>
    </div>
    <div class="box-content">
        <?php the_content(); ?>
        <?php if(get_theme_mod('restaurant_fast_food_remove_tags',true) != ''){ 
            $tags = get_the_tags(); // Retrieve the post's tags
             custom_output_tags(); 
        }?>

        <?php if(get_theme_mod('restaurant_fast_food_remove_category',true) != ''){ 
            if(has_category()){ 
                echo '<div class="post_category mt-3"> Category: ';
                the_category(', ');
                echo '</div>';
            }
        }?>
        <?php if( get_theme_mod( 'restaurant_fast_food_remove_comment',true) != ''){ 
        // If comments are open or we have at least one comment, load up the comment template
        if ( comments_open() || '0' != get_comments_number() )
        comments_template();
        }

        if ( is_singular( 'attachment' ) ) {
            
            // Parent post navigation.
            the_post_navigation( array(
                'prev_text' => _x( '<span class="meta-nav">Published in</span><span class="post-title">%title</span>', 'Parent post link', 'restaurant-fast-food' ),
            ) );
        } elseif ( is_singular( 'post' ) ) {
            // Previous/next post navigation.
            the_post_navigation( array(
                'next_text' => '<span class="meta-nav" aria-hidden="true">' . __( 'Next:', 'restaurant-fast-food' ) . '</span> ' .
                    '<span class="post-title">%title</span>',
                'prev_text' => '<span class="meta-nav" aria-hidden="true">' . __( 'Previous:', 'restaurant-fast-food' ) . '</span> ' .
                    '<span class="post-title">%title</span>',
            ) );
        } ?>
        <div class="clearfix"></div>
    </div>
       <!-- <div class="my-5"><?php get_template_part( 'template-parts/post/related-post'); echo "AQUI IMPRIME TODOS LOS POST QUE NO QUEREMOS"; ?></div> -->
       <div class="my-5">
            <?php
                $relationProfessional=get_field('relation');
                if ($relationProfessional){
                    echo '<h2>Related Professional(s)</h2>';
                    echo '<ul class="profesional-cards">';
                    foreach ($relationProfessional as $profesional) {
                        $profesional_post = get_post($profesional); // Obtener el post completo del profesional
                        ?>
                        <li class="profesional-card__list-item">
                            <a class="profesional-card" href="<?php echo get_the_permalink($profesional_post); ?>">
                                <img class="profesional-card__image" src="<?php echo get_the_post_thumbnail_url($profesional_post); ?>" alt="<?php echo get_the_title($profesional_post); ?>">
                                <span class="profesional-card__name"><?php echo get_the_title($profesional_post); ?></span>
                            </a>
                        </li>
                    <?php }
                    echo '</ul>';
                }
            ?>
       </div> 
       <div class="my-5">
            <?php
                $relationBusiness=get_field('relationbusiness');
                if ($relationBusiness){
                    echo '<h2>Related Business</h2>';
                    echo '<ul>';
                    foreach ($relationBusiness as $business) { ?>
                        <li><a href="<?php echo get_the_permalink($business) ?>"><?php echo get_the_title($business); ?></a></li>
                    <?php }
                    echo '</ul>';
                }
            ?>
       </div> 

</article>