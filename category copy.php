<?php
/**
 * The template for displaying archive pages
 *
 * @package Restaurant Fast Food
 * @subpackage restaurant_fast_food
 */

get_header(); ?>

<div class="container">
    <main id="tp_content" role="main">
        <div id="primary" class="content-area">
            <?php if ( have_posts() ) : ?>
                <div class="page-header">
                    <?php
                        the_archive_title( '<h1 class="page-title">', '</h1>' );
                        the_archive_description( '<div class="taxonomy-description">', '</div>' );
                    ?>
                </div>
            <?php endif; ?>

            <?php
            // Obtener la categoría actual
            $current_category = get_queried_object();
            // Configuración de la consulta para recuperar los custom post types "business" y "professional"
            $args = array(
                'post_type' => array( 'business', 'professional' ),
                'posts_per_page' => -1, // Mostrar todos los posts
                'tax_query' => array(
                  array(
                      'taxonomy' => 'category',
                      'field'    => 'id',
                      'terms'    => $current_category->term_id,
                  ),
              ),
            );

            // Realizar la consulta
            $query = new WP_Query( $args );

            // Mostrar los resultados de la consulta
            if ( $query->have_posts() ) :
                while ( $query->have_posts() ) : $query->the_post();
                    // Aquí puedes personalizar la visualización de cada entrada
                    ?>
                    <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
                        <h2><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h2>
                        <div class="entry-content">
                            <?php the_content(); ?>
                        </div>
                    </article>
                    <?php
                endwhile;
                wp_reset_postdata(); // Restaurar el loop original de WordPress
            else :
                // Si no se encuentran entradas
                echo '<p>No se encontraron empresas o profesionales en esta categoría.</p>';
            endif;
            ?>

        </div>
    </main>
</div>

<?php get_footer(); ?>
