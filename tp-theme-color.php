
<?php

$restaurant_fast_food_tp_theme_css = '';

//theme-color
$restaurant_fast_food_tp_color_option = get_theme_mod('restaurant_fast_food_tp_color_option');

if($restaurant_fast_food_tp_color_option != false){
$restaurant_fast_food_tp_theme_css .='button[type="submit"],.top-header,.main-navigation .menu > ul > li.highlight,.box:before,.box:after,.woocommerce #respond input#submit, .woocommerce a.button, .woocommerce button.button, .woocommerce input.button,.woocommerce #respond input#submit.alt, .woocommerce a.button.alt, .woocommerce button.button.alt, .woocommerce input.button.alt,.page-numbers,.prev.page-numbers,.next.page-numbers,span.meta-nav,#theme-sidebar button[type="submit"],#footer button[type="submit"],#comments input[type="submit"],.site-info,.book-tkt-btn a.register-btn,.logo,.search-box i,#our_project i,.more-btn a,#slider .carousel-control-prev-icon:hover, #slider .carousel-control-next-icon:hover,.error-404 [type="submit"],#slider button.owl-prev,#slider button.owl-next,#slider button[type="submit"],#product_cat_slider .product_cat_box:hover,.wc-block-cart__submit-container a,.wc-block-checkout__actions_row .wc-block-components-checkout-place-order-button,.wp-block-file .wp-block-file__button, .wp-block-button .wp-block-button__link,.wc-block-grid__product-add-to-cart.wp-block-button .wp-block-button__link,.toggle-nav i,#theme-sidebar .wp-block-search .wp-block-search__label:before,#theme-sidebar h3:before, #theme-sidebar h1.wp-block-heading:before, #theme-sidebar h2.wp-block-heading:before, #theme-sidebar h3.wp-block-heading:before,#theme-sidebar h4.wp-block-heading:before, #theme-sidebar h5.wp-block-heading:before, #theme-sidebar h6.wp-block-heading:before{';
$restaurant_fast_food_tp_theme_css .='background: '.esc_attr($restaurant_fast_food_tp_color_option).';';
$restaurant_fast_food_tp_theme_css .='}';
}
if($restaurant_fast_food_tp_color_option != false){
$restaurant_fast_food_tp_theme_css .='a ,#theme-sidebar .textwidget a,#footer .textwidget a,.comment-body a,.entry-content a,.entry-summary a,.page-template-front-page .media-links a:hover,.topbar-home i.fas.fa-phone-volume,#theme-sidebar h3,.headerbox i,.main-navigation .current_page_item > a,.readmore-btn a,#theme-sidebar h2.wp-block-heading,.wp-block-search .wp-block-search__label,.main-navigation .current_page_item > a, .main-navigation .current-menu-item > a, .main-navigation .current_page_ancestor > a,.main-navigation a:hover,.post_tag a:hover ,.nav-links a, .page-box h4 a ,.box-content li a, .comments-area a,.box-content a, .woocommerce-MyAccount-content a, .woocommerce-privacy-policy-text a, .woocommerce-cart-form a,.box-info i,.content-area a,a.added_to_cart.wc-forward,#theme-sidebar h3, #theme-sidebar h1.wp-block-heading, #theme-sidebar h2.wp-block-heading, #theme-sidebar h3.wp-block-heading,#theme-sidebar h4.wp-block-heading, #theme-sidebar h5.wp-block-heading, #theme-sidebar h6.wp-block-heading,#theme-sidebar .wp-block-search .wp-block-search__label{';
$restaurant_fast_food_tp_theme_css .='color: '.esc_attr($restaurant_fast_food_tp_color_option).';';
$restaurant_fast_food_tp_theme_css .='}';
}
if($restaurant_fast_food_tp_color_option != false){
$restaurant_fast_food_tp_theme_css .='.readmore-btn a,.post_tag a:hover,.main-navigation .current_page_item > a, .main-navigation .current-menu-item > a,.logo,#product_cat_slider hr.small-head, #product_cat_slider hr.small-head-1 {';
	$restaurant_fast_food_tp_theme_css .='border-color: '.esc_attr($restaurant_fast_food_tp_color_option).';';
$restaurant_fast_food_tp_theme_css .='}';
}
if($restaurant_fast_food_tp_color_option != false){
$restaurant_fast_food_tp_theme_css .='.page-box,#theme-sidebar section {';
    $restaurant_fast_food_tp_theme_css .='border-left-color: '.esc_attr($restaurant_fast_food_tp_color_option).';';
$restaurant_fast_food_tp_theme_css .='}';
}
if($restaurant_fast_food_tp_color_option != false){
$restaurant_fast_food_tp_theme_css .='.page-box,#theme-sidebar section {';
    $restaurant_fast_food_tp_theme_css .='border-bottom-color: '.esc_attr($restaurant_fast_food_tp_color_option).';';
$restaurant_fast_food_tp_theme_css .='}';
}
//hover color
$restaurant_fast_food_tp_color_option_link = get_theme_mod('restaurant_fast_food_tp_color_option_link');

if($restaurant_fast_food_tp_color_option_link != false){
$restaurant_fast_food_tp_theme_css .='.prev.page-numbers:focus, .prev.page-numbers:hover, .next.page-numbers:focus, .next.page-numbers:hover,span.meta-nav:hover, #comments input[type="submit"]:hover,.woocommerce #respond input#submit:hover, .woocommerce a.button:hover, .woocommerce button.button:hover, .woocommerce input.button:hover, .woocommerce #respond input#submit.alt:hover, .woocommerce a.button.alt:hover, .woocommerce button.button.alt:hover, .woocommerce input.button.alt:hover, #footer button[type="submit"]:hover, #theme-sidebar button[type="submit"]:hover,.book-tkt-btn a.register-btn:hover,.more-btn a:hover,.book-tkt-btn a.bar-btn i:hover,.page-numbers:hover,#slider button[type="submit"]:hover,.wc-block-cart__submit-container a:hover ,.main-navigation ul ul,.woocommerce ul.products li.product .onsale, .woocommerce span.onsale,#product_cat_slider .category-dot,#slider{';
	$restaurant_fast_food_tp_theme_css .='background: '.esc_attr($restaurant_fast_food_tp_color_option_link).';';
$restaurant_fast_food_tp_theme_css .='}';
}
if($restaurant_fast_food_tp_color_option_link != false){
$restaurant_fast_food_tp_theme_css .='a:hover,#theme-sidebar a:hover,.media-links i:hover, .search-box i:hover,#footer li a:hover, .page-box h4 a:hover, .box-content li a:hover ,.post_tag a:hover,.content-area a:hover,#footer p.wp-block-tag-cloud a:hover,#footer .tagcloud a:hover{';
	$restaurant_fast_food_tp_theme_css .='color: '.esc_attr($restaurant_fast_food_tp_color_option_link).';';
$restaurant_fast_food_tp_theme_css .='}';
}
if($restaurant_fast_food_tp_color_option_link != false){
$restaurant_fast_food_tp_theme_css .='#footer .tagcloud a:hover, .readmore-btn a:hover,p.wp-block-tag-cloud a:hover, .post_tag a:hover, #theme-sidebar .widget_tag_cloud a:hover,.main-navigation .current_page_ancestor > a,#footer p.wp-block-tag-cloud a:hover{';
	$restaurant_fast_food_tp_theme_css .='border-color: '.esc_attr($restaurant_fast_food_tp_color_option_link).';';
$restaurant_fast_food_tp_theme_css .='}';
}
$restaurant_fast_food_tp_footer_bg_color_option = get_theme_mod('restaurant_fast_food_tp_footer_bg_color_option');

if($restaurant_fast_food_tp_color_option_link != false){
$restaurant_fast_food_tp_theme_css .='@media screen and (max-width:1000px){';
$restaurant_fast_food_tp_theme_css .='.sidenav{';
	$restaurant_fast_food_tp_theme_css .='background: '.esc_attr($restaurant_fast_food_tp_color_option_link).';';
$restaurant_fast_food_tp_theme_css .='} }';
}
if($restaurant_fast_food_tp_footer_bg_color_option != false){
$restaurant_fast_food_tp_theme_css .='#footer{';
	$restaurant_fast_food_tp_theme_css .='background: '.esc_attr($restaurant_fast_food_tp_footer_bg_color_option).';';
$restaurant_fast_food_tp_theme_css .='}';
}