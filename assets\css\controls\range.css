/* Range Slider */
.customize-control-ms-range-slider .customize-control-title{
    display: flex;
    align-items: center;
    justify-content: space-between;
}


.customize-control-ms-range-slider .control-wrap {
    width: 100%;
    display: flex;
    align-items: center;
}

.customize-control-ms-range-slider .ms-range-slider {
    display: inline-block;
    position: relative;
    width: 67%;
    height: 4px;
    margin: 12px 0;
    background-color: rgba(0, 0, 0, 0.15);
    border-radius: 5px;
    cursor: pointer;
    -webkit-transition: background 0.5s;
    transition: background 0.5s;
}

.customize-control-ms-range-slider .ms-range-slider:hover {
    background-color: rgba(0, 0, 0, 0.2);
}

.customize-control-ms-range-slider .ui-slider-range {
    display: inline-block;
    position: absolute;
    top: 0;
    height: 100%;
    background-color: #42c7ff;
}

.customize-control-ms-range-slider .ui-slider-handle {
    height: 16px;
    width: 16px;
    background-color: #fff;
    display: inline-block;
    position: absolute;
    top: 50%;
    -webkit-transform: translateY(-50%) translateX(-4px);
    transform: translateY(-50%) translateX(-4px);
    -webkit-box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    cursor: pointer;
}

.customize-control-ms-range-slider .ms-range-slider-input {
    float: right;
    width: 33%;
}

.customize-control-ms-range-slider input.ms-slider-input {
    margin-left: 28px;
    width: 50%;
    height: 28px;
    text-align: center;
    border-radius: 4px;
    padding: 3px;
    font-size: 12px;
    font-weight: 600;
    color: #555;
}

.customize-control-ms-range-slider .ms-range-slider-unit{
    margin-left: 5px;
}


