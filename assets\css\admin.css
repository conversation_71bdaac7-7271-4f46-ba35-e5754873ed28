.restaurant-fast-food-getting-started-notice {
    padding: 20px 10px;
}
h2.restaurant-fast-food-notice-h2 {
    margin: 0 0 10px;
    font-weight: 400;
    line-height: 1.3;
}
.restaurant-fast-food-push-down {
    padding-top: 15px;
    display: inline-block;
    padding-left: 8px;
}

/** Theme about page css */
.green.button {
    background: none #5cb85c;
    border-color: #4cae4c;
    color: #ffffff;
}
.about-wrap.full-width-layout {
    max-width: 100%;
}
.about-wrap .about-description,
.about-wrap .about-text {
    font-size: 17px;
}
.about-wrap h1,.theme-description .about-text {
    margin: 0;
}
.about-wrap h2 {
    text-align: left;
}
.about-theme {
    display: -webkit-box;
    display: -ms-flexbox;
    display: -webkit-flex;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin-top: 1.105263157894737em;
    width: 100%;
}
.theme-description,
.theme-screenshot {
    width: 100%;
}
.theme-screenshot {
    order: 1;
}
.theme-description {
    margin-top: 1.105263157894737em;
    order: 2;
}
.theme-description .actions {
    margin-top: 2em;
    margin-bottom: 2em;
}
.col .button {
    margin-top: 7px;
}
.about-wrap h2 {
    font-size: 19px;
    font-weight: 600;
    margin-top: 0;
}
.about-wrap .two-col .col {
    padding: 2em 2em 1em 2em;
    background-color: #fff;
    -webkit-flex-basis: 100%;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
    max-width: 100%;
    min-width: auto;
}
pre.changelog {
    font-size: 13px;
    background-color: #f5f5f5;
    border: 1px solid #d4d4d4;
    display: block;
    line-height: 19px;
    margin-bottom: 10px;
    overflow: visible;
    overflow-y: visible;
    overflow-y: hidden;
    padding: 14px;
}
.vs-theme-table table {
    border-collapse: collapse;
}
.vs-theme-table th {
    border: 1px solid #e0e0e0;
    padding: 1em;
}
.vs-theme-table td {
    border: 1px solid #e0e0e0;
    border-collapse: collapse;
    padding: 0.8em;
    text-align: center;
}
.vs-theme-table .dashicons {
    font-size: 32px;
    font-size: 2rem;
    width: unset;
    height: unset;
}
.vs-theme-table .dashicons-saved {
    color: #4ca54c;
}
.vs-theme-table .dashicons-no-alt {
    color: #c83842;
}
.vs-theme-table tr:nth-child(even) {
    background-color: #ebebeb;
}
.vs-theme-table table {
    width: 100%;
}
.vs-theme-table th:last-child,
.vs-theme-table th:first-child {
    width: 33%;
}
@media screen and (min-width: 872px) {
    .about-wrap .two-col .col {
        -webkit-flex-basis: 50%;
        -ms-flex-preferred-size: 50%;
        flex-basis: 50%;
        max-width: 50%;
    }
    .about-theme,
    .about-wrap [class$="-col"] {
        -ms-flex-wrap: nowrap;
        flex-wrap: nowrap;
    }
    .about-wrap .theme-description {
        margin-top: 0;
        order: 1;
        padding-right: 2em;
        width: 70%;
    }
    .about-wrap .theme-screenshot {
        order: 2;
        width: 30%;
    }
}
.changelog .block {
    padding: 7px 14px;
}
.changelog .block:nth-of-type(odd) {
    background-color: #e6e6e6;
}
.changelog .block:nth-of-type(even) {
    background-color: #efefef;
}
.changelog .block span {
    display: block;
    padding: 2px;
}
.col input#myInput {
    border: none;
    width: 80px;
}
